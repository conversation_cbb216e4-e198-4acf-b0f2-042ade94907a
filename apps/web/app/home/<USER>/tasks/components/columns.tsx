"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@kit/ui/badge"
import { DataTableColumnHeader } from "./data-table-column-header"
import { Checkbox } from "@kit/ui/checkbox"
import { CompanyContent } from "~/types/company-content"
import { Circle, Loader2, CheckCircle, XCircle, Clock } from "lucide-react"
import { AssigneeAvatar } from "./assignee-avatar"

export const createColumns = (): ColumnDef<CompanyContent>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
        onClick={(e) => e.stopPropagation()} // Prevent row click when clicking checkbox
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "task_title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      return (
        <span className="max-w-[500px] truncate font-medium cursor-pointer">
          {row.getValue("task_title")}
        </span>
      )
    },
  },
  {
    accessorKey: "assigned_to",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Assignee" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-center" onClick={(e) => e.stopPropagation()}>
          <AssigneeAvatar
            taskId={row.original.id}
            assignedTo={row.original.assigned_to}
          />
        </div>
      )
    },
    enableSorting: false,
  },
  {
    accessorKey: "generation_status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Generation Status" />
    ),
    cell: ({ row }) => {
      const task = row.original;
      // OPTION 1 IMPLEMENTED: Use ONLY content_editor_template field
      // Check if content_editor_template has valid content (not null, empty array, or empty string)
      const hasContent = task.content_editor_template && 
        Array.isArray(task.content_editor_template) && 
        task.content_editor_template.length > 0;
      const isGenerating = task.is_generating;
      const hasError = task.error_generating;

      // DEBUG: Log the task data to see what's happening
      console.log('🔍 [DEBUG] Task generation status check:', {
        taskId: task.id,
        taskTitle: task.task_title,
        content_editor_template: task.content_editor_template,
        hasContent,
        isGenerating,
        hasError,
        content_editor_template_type: typeof task.content_editor_template,
        content_editor_template_length: Array.isArray(task.content_editor_template) ? task.content_editor_template.length : 'N/A'
      });

      if (isGenerating) {
        return (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            <span className="text-sm text-blue-600">Generating...</span>
          </div>
        );
      }

      if (hasError) {
        return (
          <div className="flex items-center space-x-2">
            <XCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-600">Failed</span>
          </div>
        );
      }

      if (hasContent) {
        return (
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600">Complete</span>
          </div>
        );
      }

      return (
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-500">Pending</span>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "content_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Content Type" />
    ),
    cell: ({ row }) => {
      return (
        <Badge variant="outline">{row.original.content_type}</Badge>
      )
    },
    filterFn: (row, id, value) => {
      const rowValue = row.getValue(id) as string;
      return Array.isArray(value) ? value.includes(rowValue) : rowValue === value;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.original.status || 'draft';
      
      return (
        <div className="flex w-[100px] items-center">
            <Circle className="mr-2 h-4 w-4 text-muted-foreground" />
          <span>{status.charAt(0).toUpperCase() + status.slice(1)}</span>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      const rowValue = row.getValue(id) as string || 'draft';
      return Array.isArray(value) ? value.includes(rowValue) : rowValue === value;
    },
  },
  {
    accessorKey: "scheduled_publishing_time",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Scheduled Date" />
    ),
    cell: ({ row }) => {
      const scheduledTime = row.original.scheduled_publishing_time;
      if (!scheduledTime) return null;
      
      // Format the date to show only the date part
      const date = new Date(scheduledTime);
      const formattedDate = date.toLocaleDateString();
      
      return (
        <div className="flex items-center">
          <span>{formattedDate}</span>
        </div>
      )
    },
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated" />
    ),
    cell: ({ row }) => {
      const updatedAt = row.original.updated_at;
      if (!updatedAt) return null;
      
      // Format the date and time
      const date = new Date(updatedAt);
      const formattedDate = date.toLocaleDateString();
      const formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      
      return (
        <div className="flex items-center">
          <span>{formattedDate} {formattedTime}</span>
        </div>
      )
    },
  },
  // {
  //   accessorKey: "priority",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Priority" />
  //   ),
  //   cell: ({ row }) => {
  //     const priority = priorities.find(
  //       (priority) => priority.value === row.getValue("priority")
  //     )

  //     if (!priority) {
  //       return null
  //     }

  //     return (
  //       <div className="flex items-center">
  //         {priority.icon && (
  //           <priority.icon className="mr-2 h-4 w-4 text-muted-foreground" />
  //         )}
  //         <span>{priority.label}</span>
  //       </div>
  //     )
  //   },
  //   filterFn: (row, id, value) => {
  //     return value.includes(row.getValue(id))
  //   },
  // },
  // {
  //   id: "actions",
  //   cell: ({ row }) => <DataTableRowActions row={row} />,
  // },
]
