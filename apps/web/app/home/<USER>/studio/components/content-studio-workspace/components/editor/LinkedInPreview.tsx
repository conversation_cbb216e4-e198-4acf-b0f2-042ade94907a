import React, { useEffect, useState } from "react";
import { CompanyContent } from "~/types/company-content";
import { Card } from "@kit/ui/card";
import { Avatar } from "@kit/ui/avatar";
import { <PERSON><PERSON> } from "@kit/ui/button";
import { UserIcon, X, Pause, Play, Clock } from "lucide-react";
import GeneralContentEditor from "./general-content-editor";
import { SocialProfile } from "../../index";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";
import PostDialog from "./post-dialog";
import { useImageContent } from "../../context/ContentStudioContext";
import { toast } from "@kit/ui/sonner";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@kit/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { Input } from "@kit/ui/input";
import { Calendar } from "@kit/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@kit/ui/popover";
import { CalendarIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { cn } from "@kit/ui/utils";
import { useAyrshareStatus } from "../../../../../../../../hooks/use-ayrshare-status";

// Schema for unpause dialog
const UnpauseScheduleSchema = z.object({
  scheduleDate: z.date(),
  scheduleTime: z.string(),
  timezone: z.string()
});

type UnpauseScheduleData = z.infer<typeof UnpauseScheduleSchema>;

// Comprehensive timezone options
const TIMEZONE_OPTIONS = [
  // UTC and GMT
  { value: 'UTC', label: 'UTC (GMT+0)' },
  { value: 'GMT', label: 'GMT (GMT+0)' },
  
  // Americas
  { value: 'America/Los_Angeles', label: 'Pacific Time (GMT-8/-7)' },
  { value: 'America/Denver', label: 'Mountain Time (GMT-7/-6)' },
  { value: 'America/Phoenix', label: 'Arizona (GMT-7)' },
  { value: 'America/Chicago', label: 'Central Time (GMT-6/-5)' },
  { value: 'America/New_York', label: 'Eastern Time (GMT-5/-4)' },
  { value: 'America/Halifax', label: 'Atlantic Time (GMT-4/-3)' },
  { value: 'America/St_Johns', label: 'Newfoundland (GMT-3:30/-2:30)' },
  { value: 'America/Sao_Paulo', label: 'São Paulo (GMT-3/-2)' },
  { value: 'America/Argentina/Buenos_Aires', label: 'Buenos Aires (GMT-3)' },
  { value: 'America/Noronha', label: 'Fernando de Noronha (GMT-2)' },
  { value: 'Atlantic/Cape_Verde', label: 'Cape Verde (GMT-1)' },
  
  // Europe & Africa
  { value: 'Europe/London', label: 'London (GMT+0/+1)' },
  { value: 'Europe/Dublin', label: 'Dublin (GMT+0/+1)' },
  { value: 'Europe/Paris', label: 'Paris (GMT+1/+2)' },
  { value: 'Europe/Berlin', label: 'Berlin (GMT+1/+2)' },
  { value: 'Europe/Rome', label: 'Rome (GMT+1/+2)' },
  { value: 'Europe/Madrid', label: 'Madrid (GMT+1/+2)' },
  { value: 'Europe/Amsterdam', label: 'Amsterdam (GMT+1/+2)' },
  { value: 'Europe/Stockholm', label: 'Stockholm (GMT+1/+2)' },
  { value: 'Europe/Athens', label: 'Athens (GMT+2/+3)' },
  { value: 'Europe/Helsinki', label: 'Helsinki (GMT+2/+3)' },
  { value: 'Europe/Kiev', label: 'Kyiv (GMT+2/+3)' },
  { value: 'Europe/Istanbul', label: 'Istanbul (GMT+3)' },
  { value: 'Europe/Moscow', label: 'Moscow (GMT+3)' },
  { value: 'Africa/Cairo', label: 'Cairo (GMT+2/+3)' },
  { value: 'Africa/Johannesburg', label: 'Johannesburg (GMT+2)' },
  { value: 'Africa/Lagos', label: 'Lagos (GMT+1)' },
  { value: 'Africa/Casablanca', label: 'Casablanca (GMT+0/+1)' },
  
  // Middle East & Central Asia
  { value: 'Asia/Kuwait', label: 'Kuwait (GMT+3)' },
  { value: 'Asia/Riyadh', label: 'Riyadh (GMT+3)' },
  { value: 'Asia/Baghdad', label: 'Baghdad (GMT+3)' },
  { value: 'Asia/Tehran', label: 'Tehran (GMT+3:30/+4:30)' },
  { value: 'Asia/Dubai', label: 'Dubai (GMT+4)' },
  { value: 'Asia/Baku', label: 'Baku (GMT+4)' },
  { value: 'Asia/Kabul', label: 'Kabul (GMT+4:30)' },
  { value: 'Asia/Karachi', label: 'Karachi (GMT+5)' },
  { value: 'Asia/Kolkata', label: 'Mumbai/Delhi (GMT+5:30)' },
  { value: 'Asia/Kathmandu', label: 'Kathmandu (GMT+5:45)' },
  { value: 'Asia/Dhaka', label: 'Dhaka (GMT+6)' },
  { value: 'Asia/Yangon', label: 'Yangon (GMT+6:30)' },
  
  // East Asia
  { value: 'Asia/Bangkok', label: 'Bangkok (GMT+7)' },
  { value: 'Asia/Ho_Chi_Minh', label: 'Ho Chi Minh City (GMT+7)' },
  { value: 'Asia/Jakarta', label: 'Jakarta (GMT+7)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (GMT+8)' },
  { value: 'Asia/Beijing', label: 'Beijing (GMT+8)' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong (GMT+8)' },
  { value: 'Asia/Singapore', label: 'Singapore (GMT+8)' },
  { value: 'Asia/Manila', label: 'Manila (GMT+8)' },
  { value: 'Asia/Seoul', label: 'Seoul (GMT+9)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' },
  { value: 'Australia/Adelaide', label: 'Adelaide (GMT+9:30/+10:30)' },
  { value: 'Australia/Darwin', label: 'Darwin (GMT+9:30)' },
  
  // Pacific
  { value: 'Australia/Sydney', label: 'Sydney (GMT+10/+11)' },
  { value: 'Australia/Melbourne', label: 'Melbourne (GMT+10/+11)' },
  { value: 'Australia/Brisbane', label: 'Brisbane (GMT+10)' },
  { value: 'Pacific/Guam', label: 'Guam (GMT+10)' },
  { value: 'Asia/Vladivostok', label: 'Vladivostok (GMT+10)' },
  { value: 'Pacific/Auckland', label: 'Auckland (GMT+12/+13)' },
  { value: 'Pacific/Fiji', label: 'Fiji (GMT+12/+13)' },
  { value: 'Pacific/Tongatapu', label: 'Tonga (GMT+13)' },
  { value: 'Pacific/Honolulu', label: 'Hawaii (GMT-10)' },
  { value: 'Pacific/Marquesas', label: 'Marquesas (GMT-9:30)' },
  { value: 'America/Anchorage', label: 'Alaska (GMT-9/-8)' },
];

// Function to detect user's timezone
const getUserTimezone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    return 'UTC';
  }
};

// Match the interface structure with the profileDetails from SocialsIntegrationCard
interface ProfileDetails {
  activeSocialAccounts: string[];
  displayNames: Array<{
    created: string;
    displayName: string;
    id: string;
    platform: string;
    profileUrl: string;
    userImage: string;
    username: string;
    headline?: string;
    subscriptionType?: string;
    verifiedType?: string;
    refreshDaysRemaining?: number;
    refreshRequired?: string;
    type?: string;
  }>;
  email: string | null;
  monthlyApiCalls: number;
  monthlyPostCount: number;
  refId: string;
  title: string;
  lastUpdated: string;
  nextUpdate: string;
}

// Common component for image display with remove button
function ImageWithRemoveButton({ 
  src, 
  alt, 
  className, 
  onRemove, 
  buttonSize = "default",
  overlay,
  showRemoveButton = true,
  onClick
}: {
  src: string;
  alt: string;
  className?: string;
  onRemove: () => void;
  buttonSize?: "default" | "small";
  overlay?: React.ReactNode;
  showRemoveButton?: boolean;
  onClick?: () => void;
}) {
  const buttonClasses = buttonSize === "small" 
    ? "absolute top-1 right-1 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
    : "absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity";
  
  const iconClasses = buttonSize === "small" ? "h-2 w-2" : "h-3 w-3";

  return (
    <div className="relative group">
      <img 
        src={src} 
        alt={alt}
        className={`${className} ${onClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
        onClick={onClick}
      />
      {showRemoveButton && (
        <Button
          variant="destructive"
          size="icon"
          className={buttonClasses}
          onClick={onRemove}
        >
          <X className={iconClasses} />
        </Button>
      )}
      {overlay}
    </div>
  );
}

// Component for unpause dialog with time selection
function UnpauseDialog({ 
  companyContent, 
  selectedProfile, 
  onUnpause 
}: { 
  companyContent: CompanyContent;
  selectedProfile?: SocialProfile | null;
  onUnpause: (newScheduleDate: string) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Get user's timezone as default
  const userTimezone = getUserTimezone();
  const defaultTimezone = TIMEZONE_OPTIONS.find(tz => tz.value === userTimezone)?.value || 'UTC';
  
  // Parse existing schedule date to set defaults
  const existingScheduleDate = companyContent.schedule_date ? new Date(companyContent.schedule_date) : new Date();
  const existingTime = existingScheduleDate.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' });
  
  const form = useForm<UnpauseScheduleData>({
    resolver: zodResolver(UnpauseScheduleSchema),
    defaultValues: {
      scheduleDate: existingScheduleDate,
      scheduleTime: existingTime,
      timezone: defaultTimezone,
    },
  });

  const watchScheduleDate = form.watch("scheduleDate");
  const watchScheduleTime = form.watch("scheduleTime");
  
  // Check if selected date/time is in the past
  const isPastDateTime = () => {
    if (!watchScheduleDate || !watchScheduleTime) return false;
    
    const timeParts = watchScheduleTime.split(':');
    const hours = parseInt(timeParts[0] || '0', 10);
    const minutes = parseInt(timeParts[1] || '0', 10);
    
    const selectedDateTime = new Date(watchScheduleDate);
    selectedDateTime.setHours(hours, minutes, 0, 0);
    
    return selectedDateTime < new Date();
  };

  const onSubmit = async (data: UnpauseScheduleData) => {
    setIsProcessing(true);
    
    try {
      // Parse the time (HH:MM format)
      const timeParts = data.scheduleTime.split(':');
      const hours = parseInt(timeParts[0] || '0', 10);
      const minutes = parseInt(timeParts[1] || '0', 10);
      
      // Create date object with selected date and time
      const scheduledDateTime = new Date(data.scheduleDate);
      scheduledDateTime.setHours(hours, minutes, 0, 0);
      
      // Convert to UTC for Ayrshare API
      const scheduleDate = scheduledDateTime.toISOString();
      
      await onUnpause(scheduleDate);
      setIsOpen(false);
      form.reset();
    } catch (error) {
      console.error('Error in unpause form:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center space-x-1">
          <Play className="h-3 w-3" />
          <span>Resume</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Resume Scheduled Post</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            
            {/* Date Picker */}
            <FormField
              control={form.control}
              name="scheduleDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Schedule Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Time Picker */}
            <FormField
              control={form.control}
              name="scheduleTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Schedule Time</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <Input
                        type="time"
                        className="flex-1"
                        {...field}
                        placeholder="HH:MM"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Timezone Selector */}
            <FormField
              control={form.control}
              name="timezone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Timezone</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select timezone" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {TIMEZONE_OPTIONS.map((timezone) => (
                        <SelectItem key={timezone.value} value={timezone.value}>
                          {timezone.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Warning for past dates */}
            {isPastDateTime() && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <p className="text-yellow-800 font-medium">Past Date Selected</p>
                </div>
                <p className="text-yellow-700 text-sm mt-1">
                  The selected date and time is in the past. The post will be published immediately when resumed.
                </p>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isProcessing}>
                Cancel
              </Button>
              <Button type="submit" disabled={isProcessing}>
                {isProcessing ? 'Processing...' : 'Resume Post'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// Component for scheduled post actions
function ScheduledPostActions({ 
  companyContent, 
  selectedProfile 
}: { 
  companyContent: CompanyContent;
  selectedProfile?: SocialProfile | null;
}) {
  const [isToggling, setIsToggling] = useState(false);
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  
  // Use Ayrshare status as source of truth
  const { 
    status: ayrshareStatus, 
    isLoading: isStatusLoading, 
    error: statusError,
    refetch: refetchStatus 
  } = useAyrshareStatus(
    companyContent.ayrshare_post_id, 
    (selectedProfile?.profile_key || undefined) as string | undefined,
    { enabled: !!companyContent.ayrshare_post_id && !!selectedProfile?.profile_key }
  );

  const handlePause = async () => {
    
    
    if (!companyContent.ayrshare_post_id || !selectedProfile) {
      
      toast.error('Unable to pause post: Missing post ID or profile');
      return;
    }

    setIsToggling(true);
    
    try {
      const response = await fetch('/api/integrations/ayrshare/post', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ayrsharePostId: companyContent.ayrshare_post_id,
          action: 'pause',
          companyId: workspace.account.id,
          profileKey: selectedProfile.profile_key,
          contentId: companyContent.id
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Post paused successfully');
        
        // Refetch status from Ayrshare to get updated state
        await refetchStatus();
      } else {
        toast.error(result.error || 'Failed to pause post');
      }
    } catch (error) {
      console.error('Error pausing post:', error);
      toast.error('Failed to pause post');
    } finally {
      setIsToggling(false);
    }
  };

  const handleUnpause = async (newScheduleDate: string) => {
    if (!companyContent.ayrshare_post_id || !selectedProfile) {
      toast.error('Unable to unpause post: Missing post ID or profile');
      return;
    }

    setIsToggling(true);
    
    try {
      const response = await fetch('/api/integrations/ayrshare/post', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ayrsharePostId: companyContent.ayrshare_post_id,
          action: 'unpause',
          companyId: workspace.account.id,
          profileKey: selectedProfile.profile_key,
          contentId: companyContent.id,
          newScheduleDate: newScheduleDate
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Post resumed successfully');
        
        // Refetch status from Ayrshare to get updated state
        await refetchStatus();
      } else {
        toast.error(result.error || 'Failed to resume post');
      }
    } catch (error) {
      console.error('Error resuming post:', error);
      toast.error('Failed to resume post');
    } finally {
      setIsToggling(false);
    }
  };

  // Determine status from Ayrshare or fallback to database
  const isPaused = ayrshareStatus?.status === 'paused';
  const isPublished = ayrshareStatus?.status === 'success';
  const isPending = ayrshareStatus?.status === 'pending';
  
  // Format the schedule date - use Ayrshare data if available, otherwise database
  const scheduleDate = ayrshareStatus?.scheduleDate 
    ? new Date(ayrshareStatus.scheduleDate) 
    : companyContent.schedule_date 
    ? new Date(companyContent.schedule_date) 
    : null;
  const formattedDate = scheduleDate ? scheduleDate.toLocaleString() : 'Unknown date';

  // Show loading state if fetching status
  if (isStatusLoading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <p className="text-blue-900">Checking post status...</p>
        </div>
      </div>
    );
  }

  // Show error state if failed to fetch status
  if (statusError) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-yellow-600" />
            <div>
              <p className="font-medium text-yellow-900">Unable to check post status</p>
              <p className="text-sm text-yellow-700">Using local data: {companyContent.is_paused ? 'Paused' : 'Scheduled'}</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchStatus()}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Show published state if post is published
  if (isPublished) {
    const publishedUrl = ayrshareStatus?.postUrls 
      ? Object.values(ayrshareStatus.postUrls)[0] 
      : companyContent.published_url;
      
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <p className="text-green-800 font-medium">Post is published</p>
          </div>
          <div className="flex items-center space-x-2">
            {publishedUrl && (
              <Button
                variant="outline"
                size="sm"
                asChild
              >
                <a 
                  href={publishedUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  View Post
                </a>
              </Button>
            )}
          </div>
        </div>
        <p className="text-green-700 text-sm mt-1">
          Published posts cannot be edited
        </p>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <CalendarIcon className="h-5 w-5 text-blue-600" />
          <div>
            <p className="font-medium text-blue-900">
              {isPaused ? 'Scheduled Post (Paused)' : 'Scheduled Post'}
            </p>
            <p className="text-sm text-blue-700 flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>Scheduled for: {formattedDate}</span>
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {isPaused ? (
            <UnpauseDialog 
              companyContent={companyContent}
              selectedProfile={selectedProfile}
              onUnpause={handleUnpause}
            />
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={handlePause}
              disabled={isToggling}
              className="flex items-center space-x-1"
            >
              {isToggling ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Pause className="h-3 w-3" />
                  <span>Pause</span>
                </>
              )}
            </Button>
          )}
          {companyContent.published_url && (
            <Button
              variant="outline"
              size="sm"
              asChild
            >
              <a 
                href={companyContent.published_url} 
                target="_blank" 
                rel="noopener noreferrer"
              >
                View Post
              </a>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// Component that contains the LinkedIn Preview content
export function LinkedInPreview({ selectedProfile }: {  selectedProfile?: SocialProfile | null }) {
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const { setSelectedEditorImage, setSelectedImageOption } = useImageContent();

  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];
  
  // Use Ayrshare status for posts that have been scheduled through Ayrshare
  const profileKey = (selectedProfile?.profile_key || undefined) as string | undefined;
  const ayrsharePostId = selectedCompanyContent?.ayrshare_post_id || undefined;
  
  
  
  const { 
    status: ayrshareStatus, 
    isLoading: isStatusLoading,
    error: statusError
  } = useAyrshareStatus(
    ayrsharePostId, 
    profileKey,
    { 
      enabled: !!ayrsharePostId && !!profileKey,
      refetchInterval: 30000 // Check every 30 seconds
    }
  );
  
  
  
  // Determine the actual post status
  // A post is published if Ayrshare status is 'success' AND type is NOT 'scheduled'
  const isPublished = (ayrshareStatus?.status === 'success' && ayrshareStatus?.type !== 'scheduled') || 
                     selectedCompanyContent?.is_published;
  const isScheduled = (ayrshareStatus?.status === 'pending' || ayrshareStatus?.status === 'paused' || 
                      (ayrshareStatus?.status === 'success' && ayrshareStatus?.type === 'scheduled')) || 
                     (selectedCompanyContent?.is_scheduled && !isPublished);
                     
  
  
  // Optional: Sync database when Ayrshare status changes
  React.useEffect(() => {
    if (!ayrshareStatus || !selectedCompanyContent?.id || !ayrsharePostId) return;
    
    const shouldUpdateDB = 
      (ayrshareStatus.status === 'success' && ayrshareStatus.type !== 'scheduled' && !selectedCompanyContent.is_published) ||
      (ayrshareStatus.status === 'pending' && selectedCompanyContent.is_published);
    
    if (shouldUpdateDB) {
      
      
      const updateValues: any = {};
      
      if (ayrshareStatus.status === 'success') {
        // Post was published by Ayrshare
        updateValues.is_published = true;
        updateValues.is_scheduled = false;
        updateValues.published_at = Date.now();
        
        // Get published URL from Ayrshare if available
        if (ayrshareStatus.postIds?.[0]?.postUrl && !selectedCompanyContent.published_url) {
          updateValues.published_url = ayrshareStatus.postIds[0].postUrl;
        } else if (ayrshareStatus.postUrls && !selectedCompanyContent.published_url) {
          const firstUrl = Object.values(ayrshareStatus.postUrls)[0];
          if (firstUrl) {
            updateValues.published_url = firstUrl;
          }
        }
      }
      
      // Update the database
      try {
        zero.mutate.company_content.update({
          id: selectedCompanyContent.id,
          values: updateValues
        });
      } catch (error) {
        console.error('Failed to sync database with Ayrshare status:', error);
      }
    }
  }, [ayrshareStatus?.status, selectedCompanyContent?.id, selectedCompanyContent?.is_published, ayrsharePostId, zero]);  

  // Use Ayrshare hook for posting
  // const { postToLinkedIn, loading: posting, error: ayrshareError } = useAyrsharePost();
  
  const displayName = selectedProfile?.display_name || "Your Name!";
  const headline = selectedProfile?.headline || "Your Professional Headline";

  const profilePicture = selectedProfile?.user_image || null;

  // Utility function to detect if URL is a video
  const isVideoUrl = (url: string): boolean => {
    return url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') || url.includes('.webm');
  };

  // Get typed image URLs and separate videos from images
  const allMediaUrls = (selectedCompanyContent?.image_urls as string[]) || [];
  const videoUrls = allMediaUrls.filter(isVideoUrl);
  const imageUrls = allMediaUrls.filter(url => !isVideoUrl(url));
  
  // Show videos if any exist, otherwise show images (LinkedIn only supports one or the other)
  const shouldShowVideo = videoUrls.length > 0;
  const mediaToShow = shouldShowVideo ? videoUrls.slice(0, 1) : imageUrls; // LinkedIn only supports 1 video

  // Function to remove image from the array
  const removeImage = (urlToRemove: string) => {
    const currentImages = selectedCompanyContent?.image_urls as string[] || [];
    const updatedImages = currentImages.filter((url: string) => url !== urlToRemove);
    
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || "",
      values: {
        image_urls: updatedImages
      }
    });
  };
  

  // Function to handle image click for editing
  const handleImageClick = (imageUrl: string) => {
    // Only allow editing if post is not published and not scheduled
    if (!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled) {
      setSelectedEditorImage(imageUrl);
      setSelectedImageOption('selected');
    }
  };
  
  return (
    <div className="mx-auto max-w-3xl p-6">
      {/* Post Status Section */}
      <div className="mb-4">
        {isScheduled ? (
          <ScheduledPostActions 
            companyContent={selectedCompanyContent as unknown as CompanyContent} 
            selectedProfile={selectedProfile}
          />
        ) : isPublished ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <p className="text-green-800 font-medium">Post is published</p>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                >
                  <a 
                    href={
                      ayrshareStatus?.postIds?.[0]?.postUrl || 
                      selectedCompanyContent?.published_url || 
                      '#'
                    } 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    View Post
                  </a>
                </Button>
              </div>
            </div>
            <p className="text-green-700 text-sm mt-1">
              Published posts cannot be edited
            </p>
          </div>
        ) : (
          <div className="text-right w-full">
            <PostDialog selectedProfile={selectedProfile} companyContent={selectedCompanyContent as unknown as CompanyContent} />
          </div>
        )}
      </div>
      
        <Card className="w-full p-4 bg-white shadow-sm border rounded-md overflow-hidden">
          {/* Post header with user info */}
          <div className="flex items-start space-x-3 mb-3">
            <Avatar className="h-12 w-12 rounded-full border">
              {/* if profilePicture is null, lets just use a gray background or lucide person icon */}
              {profilePicture ? (
                <img src={profilePicture} alt={displayName} />
              ) : (
                <div className="h-12 w-12 rounded-full border bg-gray-200 flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-gray-500" />
                </div>
              )}
            </Avatar>
            
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-[15px]">{displayName}</h3>
                  <p className="text-gray-500 text-xs">{headline}</p>
                  <div className="flex items-center text-xs text-gray-500 mt-1">
                    <span>Just now</span>
                    <span className="mx-1">•</span>
                    <span>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" data-supported-dps="16x16" fill="currentColor" className="mercado-match" width="16" height="16" focusable="false">
                      <path d="M8 1a7 7 0 107 7 7 7 0 00-7-7zM3 8a5 5 0 011-3l.55.55A1.5 1.5 0 015 6.62v1.07a.75.75 0 00.22.53l.56.56a.75.75 0 00.53.22H7v.69a.75.75 0 00.22.53l.56.56a.75.75 0 01.22.53V13a5 5 0 01-5-5zm6.24 4.83l2-2.46a.75.75 0 00.09-.8l-.58-1.16A.76.76 0 0010 8H7v-.19a.51.51 0 01.28-.45l.38-.19a.74.74 0 01.68 0L9 7.5l.38-.7a1 1 0 00.12-.48v-.85a.78.78 0 01.21-.53l1.07-1.09a5 5 0 01-1.54 9z"></path>
                    </svg>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Post content */}
          <div className="mb-4 whitespace-pre-line text-sm">
            <GeneralContentEditor 
              editable={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled} 
              companyContent={selectedCompanyContent as unknown as CompanyContent} 
            />
          </div>
          {mediaToShow.length > 0 && (
            <div className="mb-4">
              {shouldShowVideo ? (
                // Show video
                <div className="rounded-lg overflow-hidden border relative group">
                  <video 
                    src={mediaToShow[0]} 
                    className="w-full h-auto max-h-[400px] object-cover"
                    controls
                    loop
                    muted
                  />
                  {!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled && (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => removeImage(mediaToShow[0]!)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              ) : (
                // Show images
                <>
                  {imageUrls.length === 1 && (
                    <div className="rounded-lg overflow-hidden border">
                      <ImageWithRemoveButton
                        src={imageUrls[0]!}
                        alt="Post image"
                        className="w-full h-auto max-h-[400px] object-cover"
                        onRemove={() => removeImage(imageUrls[0]!)}
                        showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                        onClick={() => handleImageClick(imageUrls[0]!)}
                      />
                    </div>
                  )}
                  {imageUrls.length === 2 && (
                    <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden border">
                      {imageUrls.map((url, index) => (
                        <ImageWithRemoveButton
                          key={index}
                          src={url}
                          alt={`Post image ${index + 1}`}
                          className="w-full h-[200px] object-cover"
                          onRemove={() => removeImage(url)}
                          showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                          onClick={() => handleImageClick(url)}
                        />
                      ))}
                    </div>
                  )}
                  {imageUrls.length === 3 && (
                    <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden border">
                      <ImageWithRemoveButton
                        src={imageUrls[0]!}
                        alt="Post image 1"
                        className="w-full h-[200px] object-cover row-span-2"
                        onRemove={() => removeImage(imageUrls[0]!)}
                        showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                        onClick={() => handleImageClick(imageUrls[0]!)}
                      />
                      <div className="grid grid-rows-2 gap-1">
                        <ImageWithRemoveButton
                          src={imageUrls[1]!}
                          alt="Post image 2"
                          className="w-full h-[99px] object-cover"
                          onRemove={() => removeImage(imageUrls[1]!)}
                          buttonSize="small"
                          showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                          onClick={() => handleImageClick(imageUrls[1]!)}
                        />
                        <ImageWithRemoveButton
                          src={imageUrls[2]!}
                          alt="Post image 3"
                          className="w-full h-[99px] object-cover"
                          onRemove={() => removeImage(imageUrls[2]!)}
                          buttonSize="small"
                          showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                          onClick={() => handleImageClick(imageUrls[2]!)}
                        />
                      </div>
                    </div>
                  )}
                  {imageUrls.length >= 4 && (
                    <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden border">
                      <ImageWithRemoveButton
                        src={imageUrls[0]!}
                        alt="Post image 1"
                        className="w-full h-[200px] object-cover"
                        onRemove={() => removeImage(imageUrls[0]!)}
                        showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                        onClick={() => handleImageClick(imageUrls[0]!)}
                      />
                      <ImageWithRemoveButton
                        src={imageUrls[1]!}
                        alt="Post image 2"
                        className="w-full h-[200px] object-cover"
                        onRemove={() => removeImage(imageUrls[1]!)}
                        showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                        onClick={() => handleImageClick(imageUrls[1]!)}
                      />
                      <ImageWithRemoveButton
                        src={imageUrls[2]!}
                        alt="Post image 3"
                        className="w-full h-[200px] object-cover"
                        onRemove={() => removeImage(imageUrls[2]!)}
                        showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                        onClick={() => handleImageClick(imageUrls[2]!)}
                      />
                      <ImageWithRemoveButton
                        src={imageUrls[3]!}
                        alt="Post image 4"
                        className="w-full h-[200px] object-cover"
                        onRemove={() => removeImage(imageUrls[3]!)}
                        showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                        onClick={() => handleImageClick(imageUrls[3]!)}
                        overlay={
                          imageUrls.length > 4 && (
                            <div className="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center">
                              <span className="text-white text-2xl font-semibold">
                                +{imageUrls.length - 4}
                              </span>
                            </div>
                          )
                        }
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          )}
          
          {/* Engagement stats */}
          <div className="flex justify-between items-center py-2 text-xs text-gray-500 border-t border-b border-gray-200">
            <div className="flex items-center space-x-1">
              <span className="flex space-x-1">
                <span className="rounded-full bg-blue-500 w-4 h-4 flex items-center justify-center text-white">
                  👍
                </span>
              </span>
              <span>0 reactions</span>
            </div>
            <div>
              <span>0 comments</span>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex justify-around pt-2">
            <button className="flex items-center space-x-1 text-gray-500 hover:bg-gray-100 rounded-md px-3 py-1.5 text-sm">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-supported-dps="24x24" fill="currentColor" className="mercado-match" width="24" height="24" focusable="false">
                  <path d="M19.46 11l-3.91-3.91a7 7 0 01-1.69-2.74l-.49-1.47A2.76 2.76 0 0010.76 1 2.75 2.75 0 008 3.74v1.12a9.19 9.19 0 00.46 2.85L8.89 9H4.12A2.12 2.12 0 002 11.12a2.16 2.16 0 00.92 1.76A2.11 2.11 0 002 14.62a2.14 2.14 0 001.28 2 2 2 0 00-.28 1 2.12 2.12 0 002 2.12v.14A2.12 2.12 0 007.12 22h7.49a8.08 8.08 0 003.58-.84l.31-.16H21V11zM19 19h-1l-.73.37a6.14 6.14 0 01-2.69.63H7.72a1 1 0 01-1-.72l-.25-.87-.85-.41A1 1 0 015 17l.17-1-.76-.74A1 1 0 014.27 14l.66-1.09-.73-1.1a.49.49 0 01.08-.7.48.48 0 01.34-.11h7.05l-1.31-3.92A7 7 0 0110 4.86V3.75a.77.77 0 01.75-.75.75.75 0 01.71.51L12 5a9 9 0 002.13 3.5l4.5 4.5H19z"></path>
                </svg>
              </span>
              <span>Like</span>
            </button>
            <button className="flex items-center space-x-1 text-gray-500 hover:bg-gray-100 rounded-md px-3 py-1.5 text-sm">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-supported-dps="24x24" fill="currentColor" className="mercado-match" width="24" height="24" focusable="false">
                  <path d="M7 9h10v1H7zm0 4h7v-1H7zm16-2a6.78 6.78 0 01-2.84 5.61L12 22v-4H8A7 7 0 018 4h8a7 7 0 017 7zm-2 0a5 5 0 00-5-5H8a5 5 0 000 10h6v2.28L19 15a4.79 4.79 0 002-4z"></path>
                </svg>
              </span>
              <span>Comment</span>
            </button>
            <button className="flex items-center space-x-1 text-gray-500 hover:bg-gray-100 rounded-md px-3 py-1.5 text-sm">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-supported-dps="24x24" fill="currentColor" className="mercado-match" width="24" height="24" focusable="false">
                  <path d="M23 12l-4.61 7H16l4-6H8a3.92 3.92 0 00-4 3.84V17a4 4 0 00.19 1.24L5.12 21H3l-.73-2.22A6.4 6.4 0 012 16.94 6 6 0 018 11h12l-4-6h2.39z"></path>
                </svg>
              </span>
              <span>Share</span>
            </button>
          </div>
        </Card>
    </div>
  );
}
